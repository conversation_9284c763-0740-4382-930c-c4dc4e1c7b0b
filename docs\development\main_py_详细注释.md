# main.py 详细代码注释文档

## 文档信息
- **文件名**: main.py
- **功能**: 基于摄像头的矩形检测和激光点追踪系统
- **作者**: 极光车项目组
- **版本**: v1.0
- **创建日期**: 2025-01-31

## 代码结构概览

### 1. 导入模块部分
```python
from maix import image, display, app, time, camera  # MaixPy核心模块
import cv2                                          # OpenCV图像处理库
import numpy as np                                  # 数值计算库
import math                                         # 数学函数库
from micu_uart_lib import (SimpleUART, micu_printf) # 自定义串口通信库
```

**功能说明**:
- `maix`: MaixPy平台的核心功能模块
- `cv2`: 提供图像处理、计算机视觉算法
- `numpy`: 提供数组操作和数学计算
- `math`: 提供三角函数等数学运算
- `micu_uart_lib`: 自定义的串口通信封装库

---

## 2. 紫色激光检测器类 (PurpleLaserDetector)

### 类初始化
```python
class PurpleLaserDetector:
    def __init__(self, pixel_radius=3):
        self.pixel_radius = pixel_radius              # 激光点检测半径
        self.kernel = np.ones((3, 3), np.uint8)      # 形态学操作核心(3x3矩阵)
```

**参数说明**:
- `pixel_radius`: 激光点显示的圆形半径，默认3像素
- `kernel`: 用于形态学闭运算的结构元素，去除噪点

### 激光检测方法
```python
def detect(self, img):
    # 步骤1: 颜色空间转换 BGR → HSV
    hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    
    # 步骤2: 定义紫色范围 (HSV色彩空间)
    lower_purple = np.array([130, 80, 80])   # 紫色下限 [色调H, 饱和度S, 亮度V]
    upper_purple = np.array([160, 255, 255]) # 紫色上限 [色调H, 饱和度S, 亮度V]
    
    # 步骤3: 颜色掩码 - 提取紫色区域
    mask_purple = cv2.inRange(hsv, lower_purple, upper_purple)
    
    # 步骤4: 形态学闭运算 - 填补小洞，连接断开区域
    mask_purple = cv2.morphologyEx(mask_purple, cv2.MORPH_CLOSE, self.kernel)
```

**HSV颜色空间说明**:
- **H (色调)**: 130-160 对应紫色范围
- **S (饱和度)**: 80-255 确保颜色鲜艳度
- **V (亮度)**: 80-255 确保足够亮度

### 轮廓检测和坐标提取
```python
    # 步骤5: 轮廓检测
    contours_purple, _ = cv2.findContours(mask_purple, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    laser_points = []  # 存储激光点坐标
    
    # 步骤6: 遍历每个轮廓
    for cnt in contours_purple:
        # 获取最小外接矩形
        rect = cv2.minAreaRect(cnt)
        cx, cy = map(int, rect[0])  # 提取矩形中心坐标
        laser_points.append((cx, cy))
        
        # 在图像上标记激光点
        cv2.circle(img, (cx, cy), 3, (255, 0, 255), -1)      # 画紫色圆点
        cv2.putText(img, "Laser", (cx-20, cy-10),             # 添加文字标签
                   cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)
    
    return img, laser_points  # 返回标记后的图像和激光点坐标列表
```

---

## 3. 圆形轨迹点生成函数

```python
def generate_circle_points(center, radius, num_points):
    """在校正后的矩形内生成圆形轨迹点"""
    circle_points = []
    cx, cy = center  # 圆心坐标
    
    for i in range(num_points):
        # 计算每个点的角度 (0 到 2π均匀分布)
        angle = 2 * math.pi * i / num_points
        
        # 极坐标转直角坐标
        x = int(cx + radius * math.cos(angle))  # X = 圆心X + 半径*cos(角度)
        y = int(cy + radius * math.sin(angle))  # Y = 圆心Y + 半径*sin(角度)
        
        circle_points.append((x, y))
    
    return circle_points
```

**数学原理**:
- 使用极坐标公式: `x = cx + r*cos(θ)`, `y = cy + r*sin(θ)`
- 角度均匀分布: `θ = 2π * i / n`，其中i为点序号，n为总点数
- 生成的点按逆时针方向排列

---

## 4. 透视变换工具函数

### 顶点排序算法
```python
def perspective_transform(pts, target_width, target_height):
    # 步骤1: 四边形顶点排序 (左上→右上→右下→左下)
    s = pts.sum(axis=1)        # 计算每个点的坐标和 (x+y)
    tl = pts[np.argmin(s)]     # 左上角: x+y最小
    br = pts[np.argmax(s)]     # 右下角: x+y最大
    
    diff = np.diff(pts, axis=1)  # 计算每个点的坐标差 (x-y)
    tr = pts[np.argmin(diff)]    # 右上角: x-y最小
    bl = pts[np.argmax(diff)]    # 左下角: x-y最大
    
    src_pts = np.array([tl, tr, br, bl], dtype=np.float32)
```

**排序原理**:
- **左上角**: x+y值最小 (坐标都较小)
- **右下角**: x+y值最大 (坐标都较大)  
- **右上角**: x-y值最小 (x大y小)
- **左下角**: x-y值最大 (x小y大)

### 透视变换矩阵计算
```python
    # 步骤2: 定义目标矩形坐标 (标准化后的矩形)
    dst_pts = np.array([
        [0, 0],                                    # 左上角
        [target_width-1, 0],                       # 右上角  
        [target_width-1, target_height-1],         # 右下角
        [0, target_height-1]                       # 左下角
    ], dtype=np.float32)
    
    # 步骤3: 计算透视变换矩阵
    M = cv2.getPerspectiveTransform(src_pts, dst_pts)  # 正向变换矩阵
    ret, M_inv = cv2.invert(M)                         # 逆变换矩阵
    
    return M, M_inv, src_pts
```

**透视变换作用**:
- 将倾斜的四边形"拉直"成标准矩形
- `M`: 原图→标准矩形的变换矩阵
- `M_inv`: 标准矩形→原图的逆变换矩阵

---

## 5. 主程序初始化

### 设备初始化
```python
if __name__ == "__main__":
    # 硬件设备初始化
    disp = display.Display()                                    # 显示屏对象
    cam = camera.Camera(180, 120, image.Format.FMT_BGR888)      # 摄像头: 180x120分辨率, BGR格式
    laser_detector = PurpleLaserDetector()                      # 激光检测器实例
```

### 串口通信初始化
```python
    # 串口通信初始化
    uart = SimpleUART()
    if uart.init("/dev/ttyS0", 115200, set_as_global=True):     # 串口设备, 波特率, 设为全局
        print("串口初始化成功")
        uart.set_frame("", "", False)                           # 关闭帧格式，发送原始数据
    else:
        print("串口初始化失败")
        exit()
```

### 核心参数配置
```python
    # 矩形检测参数
    min_contour_area = 1000      # 最小轮廓面积 (过滤小噪点)
    max_contour_area = 10000     # 最大轮廓面积 (过滤过大区域)
    target_sides = 4             # 目标边数 (矩形=4边)
    
    # 透视变换与圆形轨迹参数
    corrected_width = 200        # 校正后矩形宽度
    corrected_height = 150       # 校正后矩形高度  
    circle_radius = 40           # 圆形轨迹半径
    circle_num_points = 12       # 圆周上的点数量
    
    # 显示参数
    fps = 0                      # 帧率计数器
    last_time = time.ticks_ms()  # 上次时间戳
```

---

## 6. 主循环 - 图像处理流程

### FPS计算
```python
while not app.need_exit():
    # 帧率计算
    current_time = time.ticks_ms()
    if current_time - last_time > 0:
        fps = 1000.0 / (current_time - last_time)  # FPS = 1000ms / 时间间隔
    last_time = current_time
```

### 图像获取和预处理
```python
    # 图像获取
    img = cam.read()                                           # 从摄像头读取图像
    img_cv = image.image2cv(img, ensure_bgr=True, copy=False)  # 转换为OpenCV格式
    output = img_cv.copy()                                     # 创建输出图像副本
```

---

## 7. 矩形检测流程

### 图像二值化
```python
    # 步骤1: 矩形检测预处理
    gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)            # 彩色→灰度
    _, binary = cv2.threshold(gray, 46, 255, cv2.THRESH_BINARY) # 灰度→二值 (阈值46)
    contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
```

**二值化说明**:
- 阈值46: 灰度值>46变白色(255)，≤46变黑色(0)
- `RETR_TREE`: 检测所有轮廓并建立层次关系
- `CHAIN_APPROX_SIMPLE`: 压缩轮廓，只保留端点

### 轮廓筛选
```python
    # 步骤2: 轮廓筛选
    quads = []
    for cnt in contours:
        area = cv2.contourArea(cnt)                            # 计算轮廓面积
        if min_contour_area < area < max_contour_area:         # 面积筛选: 1000 < area < 10000
            epsilon = 0.03 * cv2.arcLength(cnt, True)          # 近似精度 = 周长的3%
            approx = cv2.approxPolyDP(cnt, epsilon, True)      # 轮廓近似为多边形
            if len(approx) == target_sides:                    # 必须是4边形
                quads.append((approx, area))                   # 保存顶点和面积
```

### 选择最大矩形
```python
    # 步骤3: 选择最大的矩形
    inner_quads = []
    if quads:
        largest_quad = max(quads, key=lambda x: x[1])          # 按面积选最大
        inner_quads = [largest_quad]
```

---

## 8. 透视变换和圆形轨迹生成

### 透视变换处理
```python
    # 步骤4: 处理检测到的矩形
    all_circle_points = []  # 存储所有映射回原图的圆轨迹点
    
    for approx, area in inner_quads:
        # 提取四边形顶点
        pts = approx.reshape(4, 2).astype(np.float32)
        
        # 计算透视变换矩阵
        M, M_inv, src_pts = perspective_transform(pts, corrected_width, corrected_height)
```

### 圆形轨迹生成和映射
```python
        # 在标准矩形中生成圆形轨迹
        corrected_center = (corrected_width//2, corrected_height//2)  # 中心点(100, 75)
        corrected_circle = generate_circle_points(
            corrected_center, circle_radius, circle_num_points        # 半径40, 12个点
        )
        
        # 将标准矩形中的圆点映射回原图
        if M_inv is not None:
            corrected_points_np = np.array([corrected_circle], dtype=np.float32)
            original_points = cv2.perspectiveTransform(corrected_points_np, M_inv)[0]
            original_points = [(int(x), int(y)) for x, y in original_points]
            all_circle_points.extend(original_points)
```

### 可视化绘制
```python
            # 绘制映射回原图的轨迹点 (红色小圆)
            for (x, y) in original_points:
                cv2.circle(output, (x, y), 2, (0, 0, 255), -1)
        
        # 绘制矩形轮廓和中心点 (调试用)
        cv2.drawContours(output, [approx], -1, (0, 255, 0), 2)    # 绿色矩形边框
        M_moments = cv2.moments(approx)                            # 计算矩形重心
        if M_moments["m00"] != 0:
            cx = int(M_moments["m10"] / M_moments["m00"])           # 重心X坐标
            cy = int(M_moments["m01"] / M_moments["m00"])           # 重心Y坐标  
            cv2.circle(output, (cx, cy), 5, (255, 0, 0), -1)       # 蓝色中心点
```

---

## 9. 数据发送部分

### MID坐标计算和发送
```python
    # 发送内框圆轨迹点中心作为MID（格式：MID:(x,y)）
    if all_circle_points:
        # 计算所有圆轨迹点的平均坐标 (几何中心)
        center_x = sum(point[0] for point in all_circle_points) // len(all_circle_points)
        center_y = sum(point[1] for point in all_circle_points) // len(all_circle_points)
        mid_data = f"MID:({center_x},{center_y})"
        micu_printf(mid_data)  # 通过串口发送
```

### TARGET坐标发送
```python
    # 发送激光点作为TARGET（格式：TARGET:(x,y)）
    if laser_points:
        # 如果检测到多个激光点，发送第一个点作为目标
        target_x, target_y = laser_points[0]
        target_data = f"TARGET:({target_x},{target_y})"
        micu_printf(target_data)  # 通过串口发送
```

### 显示和延时
```python
    # 显示FPS信息
    cv2.putText(output, f"FPS: {fps:.1f}", (0, 10),
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
    
    time.sleep(0.005)  # 5ms延时，降低发送频率
    
    # 显示处理后的图像
    img_show = image.cv2image(output, bgr=True, copy=False)
    disp.show(img_show)
```

---

## 总结

### 整体工作流程
1. **图像获取** → 从摄像头读取180x120分辨率图像
2. **矩形检测** → 二值化→轮廓检测→筛选→选择最大矩形
3. **透视变换** → 将倾斜矩形校正为标准200x150矩形
4. **轨迹生成** → 在标准矩形中心生成半径40的圆，12个点
5. **坐标映射** → 将圆点通过逆透视变换映射回原图
6. **激光检测** → HSV颜色空间检测紫色激光点
7. **数据发送** → 发送MID(圆心)和TARGET(激光点)坐标
8. **可视化** → 在图像上绘制检测结果并显示

### 关键技术点
- **颜色空间转换**: BGR→HSV提高颜色检测精度
- **透视变换**: 校正倾斜图像，提高轨迹精度
- **形态学操作**: 去除噪点，改善检测效果
- **串口通信**: 实时发送坐标数据给控制系统

### 坐标系统
- **图像坐标**: 左上角(0,0)，右下角(179,119)
- **MID坐标**: 圆形轨迹的几何中心
- **TARGET坐标**: 第一个检测到的激光点中心
